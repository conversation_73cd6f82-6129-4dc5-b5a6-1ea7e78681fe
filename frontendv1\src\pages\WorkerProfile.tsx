import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useState } from 'react';
import { AlertTriangle, Award, Calendar, Clock, Pencil, HardHat, Mail, Phone, Shield } from 'lucide-react';
import { useQuery } from '@apollo/client';
import FloatingCard from '../components/layout/FloatingCard';
import {  SiteInfo,   TimeLog } from '../types';
import { GET_WORKER_BY_ID } from '../graphql/queries';
import { PhotoUpload } from '../components/workers/PhotoUpload';
import { AuditTrail } from '../components/common/AuditTrail';
import PayrollTab from '../components/worker/PayrollTab';
import { FILE_BASE_URL } from '../utils/constants';

// Mock data
const mockSite: SiteInfo = {
  id: "site1",
  name: "Westlands Construction Site",
  healthStatus: "green",
  workersOnSite: 42,
  activePermits: 8,
  openIncidents: 0,
  projectManager: "<PERSON>",
  location: "Waiyaki Way, Westlands, Nairobi",
  timeline: "Jan 2025 - Dec 2026",
  currentPhase: "Foundation",
  progressPercentage: 25,
  tenantId: '',
  status: 'active',
  createdAt: new Date()
};

// Mock worker will be replaced by GraphQL data

const mockTimeLogs: TimeLog[] = [
  {
    id: 'log1',
    workerId: 'w1',
    workerName: 'David Kamau',
    workerTrade: 'Carpenter',
    date: '2025-05-30',
    clockIn: '07:55',
    clockOut: '17:05',
    breakDuration: 60,
    totalHours: 8,
    overtime: 0,
    status: 'on-site',
    toolboxTalkAttended: true,
    isVerifiedByHikvision: true,
    hikvisionPersonId: 'hik_person_w1',
    terminalId: 'term1'
  },
  {
    id: 'log2',
    workerId: 'w1',
    workerName: 'David Kamau',
    workerTrade: 'Carpenter',
    date: '2025-05-29',
    clockIn: '07:50',
    clockOut: '17:10',
    breakDuration: 60,
    totalHours: 8,
    overtime: 0.25,
    status: 'on-site',
    toolboxTalkAttended: true,
    isVerifiedByHikvision: true,
    hikvisionPersonId: 'hik_person_w1',
    terminalId: 'term2'
  },
  {
    id: 'log3',
    workerId: 'w1',
    workerName: 'David Kamau',
    workerTrade: 'Carpenter',
    date: '2025-05-28',
    clockIn: '08:10',
    clockOut: '17:00',
    breakDuration: 60,
    totalHours: 7.75,
    overtime: 0,
    status: 'late',
    toolboxTalkAttended: false,
    isVerifiedByHikvision: false,
    isManuallyEdited: true,
    editReason: 'Terminal was offline - manual entry'
  },
  {
    id: 'log4',
    workerId: 'w1',
    workerName: 'David Kamau',
    workerTrade: 'Carpenter',
    date: '2025-05-27',
    clockIn: '07:45',
    clockOut: '18:15',
    breakDuration: 60,
    totalHours: 9.5,
    overtime: 1.5,
    status: 'on-site',
    toolboxTalkAttended: true,
    isVerifiedByHikvision: true,
    hikvisionPersonId: 'hik_person_w1',
    terminalId: 'term1'
  }
];

const WorkerProfile = () => {
  const { siteId, workerId } = useParams<{ siteId: string; workerId: string }>();
  const [site, _setSite] = useState<SiteInfo>(mockSite);
  const [timeLogs, _setTimeLogs] = useState<TimeLog[]>(mockTimeLogs);
  const [activeTab, setActiveTab] = useState('details');

  // Fetch worker using GraphQL query
  const { data: workerData, loading, error, refetch } = useQuery(GET_WORKER_BY_ID, {
    variables: { id: parseInt(workerId || '1') }
  });
  // workerById returns an array, so we take the first element
  const worker = workerData?.workerById?.[0];

  // Handle loading and error states
  if (loading) {
    return (
      <FloatingCard title="Loading Worker Profile..." breadcrumbs={[]}>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
        </div>
      </FloatingCard>
    );
  }

  if (error || !worker) {
    return (
      <FloatingCard title="Error Loading Worker" breadcrumbs={[]}>
        <div className="text-center py-12">
          <p className="text-red-600 mb-4">
            {error ? `Failed to load worker: ${error.message}` : 'Worker not found'}
          </p>
          <button
            onClick={() => refetch()}
            className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
          >
            Retry
          </button>
        </div>
      </FloatingCard>
    );
  }

  // Photo upload handlers
  const handlePhotoUpload = async (file: File): Promise<string> => {
    // TODO: Implement GraphQL mutation for photo upload
    // This will call the backend Hikvision service
    const formData = new FormData();
    formData.append('photo', file);
    formData.append('workerId', worker?.id?.toString() || '0');

    try {
      const response = await fetch('/api/workers/photo', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) throw new Error('Upload failed');

      const result = await response.json();

      // TODO: Update worker state with new photo URL
      // setWorker(prev => ({ ...prev, photoUrl: result.photoUrl }));

      return result.photoUrl;
    } catch (error) {
      console.error('Photo upload error:', error);
      throw error;
    }
  };

  const handlePhotoDelete = async (): Promise<boolean> => {
    // TODO: Implement GraphQL mutation for photo deletion
    try {
      const response = await fetch(`/api/workers/${worker?.id || 0}/photo`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Deletion failed');

      // TODO: Update worker state to remove photo URL
      // setWorker(prev => ({ ...prev, photoUrl: undefined }));

      return true;
    } catch (error) {
      console.error('Photo deletion error:', error);
      throw error;
    }
  };

	const breadcrumbs = [
		{ name: "Dashboard", path: "/" },
		{ name: site.name, path: `/sites/${siteId}/dashboard` },
		{ name: "Workers", path: `/sites/${siteId}/workers` },
		{ name: worker?.name || 'Worker', path: `/sites/${siteId}/workers/${workerId}` },
	];

  return (
    <FloatingCard title={`${worker?.name || 'Worker'} - Profile`} breadcrumbs={breadcrumbs}>
      {/* Edit Button */}
      <div className="mb-6 flex justify-end">
        <Link
          to={`/sites/${siteId}/workers/${workerId}/edit`}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
        >
          <Pencil className="h-4 w-4 mr-2" />
          Edit Worker
        </Link>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            className={`${
              activeTab === 'details'
                ? 'border-green-500 text-green-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            onClick={() => setActiveTab('details')}
          >
            Details
          </button>
          <button
            className={`${
              activeTab === 'trainings'
                ? 'border-green-500 text-green-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            onClick={() => setActiveTab('trainings')}
          >
            Trainings & Certifications
          </button>
          <button
            className={`${
              activeTab === 'timeLogs'
                ? 'border-green-500 text-green-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            onClick={() => setActiveTab('timeLogs')}
          >
            Time Logs
          </button>
          <button
            className={`${
              activeTab === 'incidents'
                ? 'border-green-500 text-green-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            onClick={() => setActiveTab('incidents')}
          >
            Incidents
          </button>
          <button
            className={`${
              activeTab === 'payroll'
                ? 'border-green-500 text-green-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            onClick={() => setActiveTab('payroll')}
          >
            Payroll
          </button>
        </nav>
      </div>

      {/* Details Tab */}
      {activeTab === 'details' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Worker Info */}
          <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
            <div className="flex items-start justify-between mb-6">
              <div className="flex items-center">
                <div className="h-20 w-20 rounded-full overflow-hidden mr-4">
                  {worker?.profilePictureUrl ? (
                    <img src={`${FILE_BASE_URL}${worker.profilePictureUrl}`} alt={worker?.name || 'Worker'} className="h-full w-full object-cover" />
                  ) : (
                    <div className="h-full w-full bg-gray-200 flex items-center justify-center">
                      <span className="text-2xl text-gray-500 font-medium">
                        {worker?.name?.charAt(0) || '?'}
                      </span>
                    </div>
                  )}
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-800">{worker?.name || 'Worker Name'}</h2>
                  <p className="text-sm text-gray-500 mt-1">ID: {worker?.nationalId || 'N/A'}</p>
                  <p className="text-sm text-gray-500">Company: {worker?.company || 'N/A'}</p>
                  <div className="mt-2">
                    <span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                      Active
                    </span>
                  </div>
                </div>
              </div>
              <button className="text-gray-400 hover:text-gray-600">
                <Pencil className="h-5 w-5" />
              </button>
            </div>
            
            <div className="border-t border-gray-100 pt-4">
              <h3 className="text-sm font-medium text-gray-500 mb-3">Contact Information</h3>
              <div className="space-y-3">
                <div className="flex items-center text-sm">
                  <Phone className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-gray-700">{worker?.phoneNumber || 'N/A'}</span>
                </div>
                {worker?.email && (
                  <div className="flex items-center text-sm">
                    <Mail className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-700">{worker.email}</span>
                  </div>
                )}
                {worker?.dateOfBirth && (
                  <div className="flex items-center text-sm">
                    <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-700">Born: {new Date(worker.dateOfBirth).toLocaleDateString()}</span>
                  </div>
                )}
                <div className="flex items-center text-sm">
                  <span className="text-gray-700">Gender: {worker?.gender || 'N/A'}</span>
                </div>
              </div>
            </div>
            
            <div className="border-t border-gray-100 pt-4 mt-4">
              <h3 className="text-sm font-medium text-gray-500 mb-3">Trades</h3>
              <div className="flex flex-wrap gap-2">
                {worker?.trades?.map((trade: any) => (
                  <span key={trade.id} className="bg-blue-100 text-blue-700 px-2 py-1 rounded-md text-xs" title={trade.description}>
                    {trade.name}
                  </span>
                )) || <span className="text-gray-500 text-sm">No trades assigned</span>}
              </div>
            </div>

            <div className="border-t border-gray-100 pt-4 mt-4">
              <h3 className="text-sm font-medium text-gray-500 mb-3">Skills</h3>
              <div className="flex flex-wrap gap-2">
                {worker?.skills?.map((skill: any) => (
                  <span key={skill.id} className="bg-green-100 text-green-700 px-2 py-1 rounded-md text-xs" title={skill.description}>
                    {skill.name}
                  </span>
                )) || <span className="text-gray-500 text-sm">No skills assigned</span>}
              </div>
            </div>
            
            <div className="border-t border-gray-100 pt-4 mt-4">
              <h3 className="text-sm font-medium text-gray-500 mb-3">Performance Rating</h3>
              <div className="flex items-center">
                <span className="text-lg font-bold text-yellow-600 mr-2">{worker?.rating?.toFixed(1) || '0.0'}</span>
                <div className="flex">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <svg
                      key={star}
                      className={`h-5 w-5 ${
                        star <= (worker?.rating || 0) ? 'text-yellow-400' : 'text-gray-300'
                      }`}
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
                <button className="ml-auto text-green-500 hover:text-green-600 text-xs font-medium">
                  Update Rating
                </button>
              </div>
            </div>

            <div className="border-t border-gray-100 pt-4 mt-4">
              <h3 className="text-sm font-medium text-gray-500 mb-3">Work Information</h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Total Man Hours:</span>
                  <span className="text-gray-900 font-medium">{worker?.manHours?.toLocaleString() || '0'}</span>
                </div>
                {worker?.inductionDate && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Induction Date:</span>
                    <span className="text-gray-900">{new Date(worker.inductionDate).toLocaleDateString()}</span>
                  </div>
                )}
                {worker?.medicalCheckDate && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Medical Check:</span>
                    <span className="text-gray-900">{new Date(worker.medicalCheckDate).toLocaleDateString()}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Right Column - Quick Stats & Actions */}
          <div className="lg:col-span-2 space-y-6">
            {/* Photo Upload Section */}
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <PhotoUpload
                workerId={worker?.id || 0}
                currentPhotoUrl={worker?.profilePictureUrl}
                onPhotoUpload={handlePhotoUpload}
                onPhotoDelete={handlePhotoDelete}
              />
            </div>
            {/* Quick Stats */}
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Stats</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-500">Site Attendance</span>
                    <Calendar className="h-4 w-4 text-gray-400" />
                  </div>
                  <p className="text-2xl font-semibold text-gray-800">98%</p>
                  <p className="text-xs text-gray-500 mt-1">Last 30 days</p>
                </div>
                
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-500">Total Hours</span>
                    <Clock className="h-4 w-4 text-gray-400" />
                  </div>
                  <p className="text-2xl font-semibold text-gray-800">{worker?.manHours?.toLocaleString() || '0'}</p>
                  <p className="text-xs text-gray-500 mt-1">Lifetime total</p>
                </div>
                
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-500">Toolbox Talks</span>
                    <Shield className="h-4 w-4 text-gray-400" />
                  </div>
                  <p className="text-2xl font-semibold text-gray-800">90%</p>
                  <p className="text-xs text-gray-500 mt-1">Attendance rate</p>
                </div>
              </div>
            </div>
            
            {/* Quick Actions */}
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button className="flex items-center justify-between bg-green-50 hover:bg-green-100 text-green-700 font-medium py-3 px-4 rounded transition-colors">
                  <div className="flex items-center">
                    <Award className="h-5 w-5 mr-2" />
                    <span>Record New Certification</span>
                  </div>
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">+</span>
                </button>
                
                <button className="flex items-center justify-between bg-blue-50 hover:bg-blue-100 text-blue-700 font-medium py-3 px-4 rounded transition-colors">
                  <div className="flex items-center">
                    <HardHat className="h-5 w-5 mr-2" />
                    <span>Assign Training</span>
                  </div>
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">+</span>
                </button>
                
                <button className="flex items-center justify-between bg-yellow-50 hover:bg-yellow-100 text-yellow-700 font-medium py-3 px-4 rounded transition-colors">
                  <div className="flex items-center">
                    <AlertTriangle className="h-5 w-5 mr-2" />
                    <span>Report Incident</span>
                  </div>
                  <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">!</span>
                </button>
                
                <button className="flex items-center justify-between bg-red-50 hover:bg-red-100 text-red-700 font-medium py-3 px-4 rounded transition-colors">
                  <span>Remove from Site</span>
                </button>
              </div>
            </div>

            {/* Audit Trail Section */}
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <AuditTrail
                entity={{
                  createdAt: worker?.createdAt,
                  createdBy: worker?.createdBy,
                  updatedAt: worker?.updatedAt,
                  updatedBy: worker?.updatedBy,
                }}
                entityType="Worker"
                entityId={worker?.id || 0}
              />
            </div>
          </div>
        </div>
      )}

      {/* Trainings & Certifications Tab */}
      {activeTab === 'trainings' && (
        <div className="space-y-6">
          {/* Certifications Section */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-800">Certifications</h3>
              <button className="text-green-500 hover:text-green-600 text-sm font-medium">
                + Add Certification
              </button>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Certification
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Issue Date
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Expiry Date
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {worker?.certifications?.map((certification: any) => (
                    <tr key={certification.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {certification.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(certification.issueDate).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(certification.expiryDate).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          certification.status === 'valid' ? 'bg-green-100 text-green-800' :
                          certification.status === 'expiring' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {certification.status === 'valid' ? 'Valid' :
                           certification.status === 'expiring' ? 'Expiring Soon' :
                           'Expired'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button className="text-green-500 hover:text-green-600">
                          View
                        </button>
                        <span className="px-2 text-gray-300">|</span>
                        <button className="text-blue-500 hover:text-blue-600">
                          Renew
                        </button>
                      </td>
                    </tr>
                  ))}
                  {(!worker.certifications || worker.certifications.length === 0) && (
                    <tr>
                      <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                        <div className="flex flex-col items-center">
                          <Award className="h-8 w-8 text-gray-400 mb-2" />
                          <p>No certifications recorded yet</p>
                          <button className="mt-2 text-green-500 hover:text-green-600 text-sm font-medium">
                            Add First Certification
                          </button>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
          
          {/* Trainings Section */}
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-800">Trainings</h3>
              <button className="text-green-500 hover:text-green-600 text-sm font-medium">
                + Record Training
              </button>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Training
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Completion Date
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Expiry Date
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {worker?.trainingHistory?.map((trainingHistory: any) => (
                    <tr key={trainingHistory.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {trainingHistory.training?.name || 'Training Name'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(trainingHistory.completionDate).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {trainingHistory.expiryDate ? new Date(trainingHistory.expiryDate).toLocaleDateString() : 'No expiry'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          trainingHistory.status === 'Completed' ? 'bg-green-100 text-green-800' :
                          trainingHistory.status === 'Expired' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {trainingHistory.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button className="text-green-500 hover:text-green-600">
                          View
                        </button>
                        <span className="px-2 text-gray-300">|</span>
                        <button className="text-blue-500 hover:text-blue-600">
                          Renew
                        </button>
                      </td>
                    </tr>
                  ))}
                  {(!worker?.trainingHistory || worker.trainingHistory.length === 0) && (
                    <tr>
                      <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                        <div className="flex flex-col items-center">
                          <HardHat className="h-8 w-8 text-gray-400 mb-2" />
                          <p>No training history recorded yet</p>
                          <button className="mt-2 text-green-500 hover:text-green-600 text-sm font-medium">
                            Record First Training
                          </button>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

			{/* Time Logs Tab */}
			{activeTab === "timeLogs" && (
				<div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
					<div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
						<h3 className="text-lg font-semibold text-gray-800">Time Logs</h3>
						<div className="flex items-center space-x-3">
							<select className="border border-gray-300 rounded-md text-sm p-2">
								<option value="current-month">Current Month</option>
								<option value="last-month">Last Month</option>
								<option value="custom">Custom Range</option>
							</select>
							<button className="text-green-500 hover:text-green-600 text-sm font-medium">
								Request Manual Entry
							</button>
						</div>
					</div>
					<div className="overflow-x-auto">
						<table className="min-w-full divide-y divide-gray-200">
							<thead className="bg-gray-50">
								<tr>
									<th
										scope="col"
										className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
									>
										Date
									</th>
									<th
										scope="col"
										className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
									>
										Clock In
									</th>
									<th
										scope="col"
										className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
									>
										Clock Out
									</th>
									<th
										scope="col"
										className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
									>
										Break
									</th>
									<th
										scope="col"
										className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
									>
										Total Hours
									</th>
									<th
										scope="col"
										className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
									>
										OT Hours
									</th>
									<th
										scope="col"
										className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
									>
										Status
									</th>
									<th
										scope="col"
										className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
									>
										Toolbox Talk
									</th>
								</tr>
							</thead>
							<tbody className="bg-white divide-y divide-gray-200">
								{timeLogs.map((log) => (
									<tr key={log.id}>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
											{log.date}
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
											{log.clockIn}
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
											{log.clockOut || "-"}
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
											{log.breakDuration} min
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
											{log.totalHours} hrs
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
											{log.overtime || 0} hrs
										</td>
										<td className="px-6 py-4 whitespace-nowrap">
											<span
												className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
													log.status === "on-site"
														? "bg-green-100 text-green-800"
														: log.status === "late"
															? "bg-yellow-100 text-yellow-800"
															: "bg-red-100 text-red-800"
												}`}
											>
												{log.status === "on-site"
													? "On Time"
													: log.status === "late"
														? "Late"
														: "Absent"}
											</span>
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
											{log.toolboxTalkAttended ? (
												<span className="text-green-500">Attended</span>
											) : (
												<span className="text-red-500">Missed</span>
											)}
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>
				</div>
			)}

      {/* Incidents Tab */}
      {activeTab === 'incidents' && (
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
          <div className="text-center py-10">
            <AlertTriangle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Incidents Reported</h3>
            <p className="text-gray-500 max-w-md mx-auto mb-6">
              This worker has no recorded incidents on this site. Safety first!
            </p>
            <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-500 hover:bg-green-600">
              <AlertTriangle className="h-4 w-4 mr-2" />
              Report New Incident
            </button>
          </div>
        </div>
      )}

      {/* Payroll Tab */}
      {activeTab === 'payroll' && (
        <PayrollTab
          workerId={worker?.id?.toString() || '0'}
          workerName={worker?.name || 'Worker'}
        />
      )}
    </FloatingCard>
  );
};

export default WorkerProfile;
