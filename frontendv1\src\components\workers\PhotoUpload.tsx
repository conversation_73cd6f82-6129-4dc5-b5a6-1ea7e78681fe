import React, { useState } from 'react';
import { Camera, Trash2 } from 'lucide-react';
import { useUploadWorkerPhoto, useDeleteWorkerPhoto, useRegisterWorkerFace } from '../../hooks/useGraphQL';
import { FILE_BASE_URL } from '../../utils/constants';

interface PhotoUploadProps {
  workerId: number;
  currentPhotoUrl?: string;
  onPhotoUpload?: (file: File) => Promise<string>;
  onPhotoDelete?: () => Promise<boolean>;
  onPhotoUpdated?: (photoUrl?: string) => void;
}

export const PhotoUpload: React.FC<PhotoUploadProps> = ({
  workerId,
  currentPhotoUrl,
  onPhotoUpload,
  onPhotoDelete,
  onPhotoUpdated,
}) => {
  const [uploading, setUploading] = useState(false);
  const [deleting, setDeleting] = useState(false);

  // GraphQL hooks
  const { mutate: uploadWorkerPhoto } = useUploadWorkerPhoto();
  const { mutate: deleteWorkerPhoto } = useDeleteWorkerPhoto();
  const { mutate: registerWorkerFace } = useRegisterWorkerFace();

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('File size must be less than 10MB');
      return;
    }

    setUploading(true);
    try {
      if (onPhotoUpload) {
        // Use custom upload handler if provided
        await onPhotoUpload(file);
      } else {
        // Use GraphQL mutation
        const result = await uploadWorkerPhoto({
          workerId,
          photo: file,
        });

        if (result.data?.uploadWorkerPhoto) {
          const { photoUrl, hikvisionRegistered } = result.data.uploadWorkerPhoto;

          // Register face with Hikvision if photo upload was successful
          if (photoUrl && !hikvisionRegistered) {
            try {
              await registerWorkerFace({
                workerId,
                photo: file,
              });
            } catch (faceRegError) {
              console.warn('Face registration failed:', faceRegError);
              // Don't fail the entire upload if face registration fails
            }
          }

          onPhotoUpdated?.(photoUrl);
        }
      }
    } catch (error) {
      console.error('Photo upload failed:', error);
      alert('Photo upload failed. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this photo? This will also remove the worker from the face recognition system.')) return;

    setDeleting(true);
    try {
      if (onPhotoDelete) {
        // Use custom delete handler if provided
        await onPhotoDelete();
      } else {
        // Use GraphQL mutation
        const result = await deleteWorkerPhoto({
          workerId,
        });

        if (result.data?.deleteWorkerPhoto?.success) {
          onPhotoUpdated?.(undefined);
        }
      }
    } catch (error) {
      console.error('Photo deletion failed:', error);
      alert('Photo deletion failed. Please try again.');
    } finally {
      setDeleting(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-700">Worker Photo</h4>
        <span className="text-xs text-gray-500">For face recognition system</span>
      </div>

      <div className="flex items-center space-x-4">
        {/* Photo Display */}
        <div className="flex-shrink-0">
          {currentPhotoUrl ? (
            <img
              src={`${FILE_BASE_URL}${currentPhotoUrl}`}
              alt="Worker photo"
              className="h-20 w-20 rounded-full object-cover border-2 border-gray-300"
            />
          ) : (
            <div className="h-20 w-20 rounded-full bg-gray-200 flex items-center justify-center">
              <Camera className="h-8 w-8 text-gray-400" />
            </div>
          )}
        </div>

        {/* Upload/Delete Controls */}
        <div className="flex-1 space-y-2">
          <div>
            <label className="cursor-pointer inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
              <Camera className="h-4 w-4 mr-2" />
              {uploading ? 'Uploading...' : currentPhotoUrl ? 'Change Photo' : 'Upload Photo'}
              <input
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                disabled={uploading}
                className="sr-only"
              />
            </label>
          </div>

          {currentPhotoUrl && (
            <button
              onClick={handleDelete}
              disabled={deleting}
              className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {deleting ? 'Deleting...' : 'Delete Photo'}
            </button>
          )}
        </div>
      </div>

      <div className="text-xs text-gray-500">
        <p>• Recommended: High-quality front-facing photo</p>
        <p>• File formats: JPG, PNG (max 10MB)</p>
        <p>• Used for automated attendance tracking</p>
      </div>
    </div>
  );
};
