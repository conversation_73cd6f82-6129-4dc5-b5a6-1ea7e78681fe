import React, { useRef, useState } from "react";
import { useMutation, useQuery } from "@apollo/client";
import { toast } from "react-toastify";
import {
  User,
  Building,
  Phone,
  Mail,
  Calendar,
  Camera,
  Upload,
  Save,
  X,
  AlertCircle,
  IdCard,
  Plus,
  File as FileIcon,
} from "lucide-react";
import { CREATE_WORKER_WITH_TRAINING } from "../../graphql/mutations";
import {
  GET_ALL_TRAININGS,
  GET_ALL_TRADES,
  GET_ALL_SKILLS,
} from "../../graphql/queries";
import {
  CreateWorkerWithTrainingInput,
  DocumentFileInput,
  Gender,
  WorkerTrainingInput,
} from "../../types/graphql";

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

interface CreateWorkerFormProps {
  onSuccess?: (worker: any) => void;
  onCancel?: () => void;
  useDummyData?: boolean;
}

interface FormData {
  name: string;
  company: string;
  nationalId: string;
  gender: Gender;
  phoneNumber: string;
  dateOfBirth: string;
  trainings: WorkerTrainingInput[];
  tradeIds: number[];
  skillIds: number[];
  mpesaNumber: string;
  email: string;
  inductionDate: string;
  medicalCheckDate: string;
  profilePicture: File | null;
  signature?: File | null;
  documents?: DocumentFileInput[];
}

interface FormErrors {
  [key: string]: string;
}

const CreateWorkerForm: React.FC<CreateWorkerFormProps> = ({
  onSuccess,
  onCancel,
}) => {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    company: "",
    nationalId: "",
    gender: "MALE",
    phoneNumber: "",
    dateOfBirth: "",
    trainings: [],
    tradeIds: [],
    skillIds: [],
    mpesaNumber: "",
    email: "",
    inductionDate: "",
    medicalCheckDate: "",
    signature: null,
    documents: [],
    profilePicture: null,
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [photoFile, setPhotoFile] = useState<File | null>(null);
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  // --- STATE for training selection and document upload ---
  const [selectedTrainings, setSelectedTrainings] = useState<number[]>([]); // training ids
  const [trainingDocuments, setTrainingDocuments] = useState<
    Record<number, DocumentFileInput[]>
  >({}); // {trainingId: [docs]}
  const [generalDocuments, setGeneralDocuments] = useState<DocumentFileInput[]>(
    []
  );

  const [createWorkerWithTraining] = useMutation(CREATE_WORKER_WITH_TRAINING, {
    onCompleted: (data) => {
      // Show success toast
      toast.success(`Worker "${data.createWorkerWithTraining.name}" created successfully!`);
      clearForm();
      onSuccess?.(data.createWorkerWithTraining);
    },
    onError: (error) => {
      console.error("Error creating worker:", error);
      toast.error(`Failed to create worker: ${error.message}`);
    },
  });

  // GraphQL queries for dropdown data
  const {
    data: trainingsData,
    loading: trainingsLoading,
    error: trainingsError,
  } = useQuery(GET_ALL_TRAININGS, {
    onError: (error) => {
      console.error("Error loading trainings:", error);
      toast.error("Failed to load trainings data");
    },
  });
  const {
    data: tradesData,
    loading: tradesLoading,
    error: tradesError,
  } = useQuery(GET_ALL_TRADES, {
    onError: (error) => {
      console.error("Error loading trades:", error);
      toast.error("Failed to load trades data");
    },
  });
  const {
    data: skillsData,
    loading: skillsLoading,
    error: skillsError,
  } = useQuery(GET_ALL_SKILLS, {
    onError: (error) => {
      console.error("Error loading skills:", error);
      toast.error("Failed to load skills data");
    },
  });

  // Get data from GraphQL
  const trainings = trainingsData?.allTrainings || [];
  const trades = tradesData?.allTrades || [];
  const skills = skillsData?.allSkills || [];

  const isLoadingDropdownData =
    trainingsLoading || tradesLoading || skillsLoading;
  const hasDropdownErrors = trainingsError || tradesError || skillsError;

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    // Required field validations
    !formData.name.trim()
      ? (newErrors.name = "Name is required")
      : delete newErrors.name;
    !formData.company.trim()
      ? (newErrors.company = "Company is required")
      : delete newErrors.company;
    !formData.nationalId.trim()
      ? (newErrors.nationalId = "National ID is required")
      : delete newErrors.nationalId;
    !formData.gender
      ? (newErrors.gender = "Gender is required")
      : delete newErrors.gender;
    !formData.phoneNumber.trim()
      ? (newErrors.phoneNumber = "Phone number is required")
      : delete newErrors.phoneNumber;
    !formData.mpesaNumber.trim()
      ? (newErrors.mpesaNumber = "Mpesa number is required")
      : delete newErrors.mpesaNumber;
    !formData.email.trim()
      ? (newErrors.email = "Email is required")
      : delete newErrors.email;
    !formData.inductionDate.trim()
      ? (newErrors.inductionDate = "Induction date is required")
      : delete newErrors.inductionDate;
    !formData.medicalCheckDate.trim()
      ? (newErrors.medicalCheckDate = "Medical check date is required")
      : delete newErrors.medicalCheckDate;
    !formData.dateOfBirth.trim()
      ? (newErrors.dateOfBirth = "Date of birth is required")
      : delete newErrors.dateOfBirth;

    // Format validations
    if (formData.nationalId && !/^\d{8,12}$/.test(formData.nationalId)) {
      newErrors.nationalId = "National ID must be 8-12 digits";
    } else if (formData.nationalId.trim()) {
      delete newErrors.nationalId;
    }

    if (
      formData.phoneNumber &&
      !/^[+]?[\d\s-()]{10,15}$/.test(formData.phoneNumber)
    ) {
      newErrors.phoneNumber = "Please enter a valid phone number";
    } else if (formData.phoneNumber.trim()) {
      delete newErrors.phoneNumber;
    }
    // Validate mpesa number
    if (
      formData.mpesaNumber &&
      !/^[+]?[\d\s-()]{10,15}$/.test(formData.mpesaNumber)
    ) {
      newErrors.mpesaNumber = "Please enter a valid mpesa number";
    } else if (formData.mpesaNumber.trim()) {
      delete newErrors.mpesaNumber;
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    } else if (formData.email.trim()) {
      delete newErrors.email;
    }

    // Date validations
    if (formData.dateOfBirth) {
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();

      if (age < 18 || age > 80) {
        newErrors.dateOfBirth = "Age must be between 18 and 80 years";
      } else if (formData.dateOfBirth.trim()) {
        delete newErrors.dateOfBirth;
      }
    }

    if (
      formData.inductionDate &&
      new Date(formData.inductionDate) > new Date()
    ) {
      newErrors.inductionDate = "Induction date cannot be in the future";
    } else if (formData.inductionDate.trim()) {
      delete newErrors.inductionDate;
    }

    if (
      formData.medicalCheckDate &&
      new Date(formData.medicalCheckDate) > new Date()
    ) {
      newErrors.medicalCheckDate = "Medical check date cannot be in the future";
    } else if (formData.medicalCheckDate.trim()) {
      delete newErrors.medicalCheckDate;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleMultiSelectChange = (
    field: "tradeIds" | "skillIds",
    value: number
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: prev[field].includes(value)
        ? prev[field].filter((id) => id !== value)
        : [...prev[field], value],
    }));
  };


  const handlePhotoSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      toast.error("Please select a file");
      setErrors((prev) => ({ ...prev, photo: "Please select a file" }));
      return;
    }

    // Validate file type
    if (!file.type.startsWith("image/")) {
      toast.error("Please select an image file");
      setErrors((prev) => ({ ...prev, photo: "Please select an image file" }));
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > MAX_FILE_SIZE) {
      toast.error("File size must be less than 10MB");
      setErrors((prev) => ({
        ...prev,
        photo: `File size must be less than ${MAX_FILE_SIZE / 1024 / 1024}MB`,
      }));
      return;
    }

    setPhotoFile(file);

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPhotoPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleTakePhoto = () => {
    // Placeholder for camera functionality
    toast.info("Camera functionality will be implemented later");
  };

  // Helper function to convert date string to DateTime format
  const formatDateTimeForGraphQL = (dateString: string): string | null => {
    if (!dateString) return null;
    // Convert date string (YYYY-MM-DD) to ISO DateTime string
    const date = new Date(dateString + "T00:00:00.000Z");
    return date.toISOString();
  };

  // Helper function to clear the form
  function clearForm() {
    setFormData({
      name: "",
      company: "",
      nationalId: "",
      gender: "MALE",
      phoneNumber: "",
      dateOfBirth: "",
      trainings: [],
      tradeIds: [],
      skillIds: [],
      mpesaNumber: "",
      email: "",
      inductionDate: "",
      medicalCheckDate: "",
      signature: null,
      documents: [],
      profilePicture: null,
    });
    setErrors({});
    setPhotoFile(null);
    setPhotoPreview(null);
    setSelectedTrainings([]);
    setTrainingDocuments({});
    setGeneralDocuments([]);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;
    if (!photoFile) {
      toast.error("Please select a photo");
      return;
    }

    const newWorker: CreateWorkerWithTrainingInput = {
      name: formData.name,
      company: formData.company,
      nationalId: formData.nationalId,
      gender: formData.gender,
      phoneNumber: formData.phoneNumber,
      dateOfBirth: formData.dateOfBirth,
      trainings: selectedTrainings.map((trainingId) => ({
        trainingId,
        documents: trainingDocuments[trainingId] || [],
      })),
      tradeIds: formData.tradeIds,
      skillIds: formData.skillIds,
      mpesaNumber: formData.mpesaNumber,
      email: formData.email,
      inductionDate:
        formatDateTimeForGraphQL(formData.inductionDate) || undefined,
      medicalCheckDate:
        formatDateTimeForGraphQL(formData.medicalCheckDate) || undefined,
      profilePicture: photoFile,
      // signature: formData.signature,
      documents: generalDocuments,
    };
    // console.log("newWorker", newWorker);
    setIsSubmitting(true);
    try {
      await createWorkerWithTraining({
        variables: {
          input: newWorker
        }
      });
    } catch (error) {
      // Error handling is done in the mutation's onError callback
      console.error('Error creating worker:', error);
    } finally {
      setIsSubmitting(false);
    }
  };


  const handleTrainingCheckbox = (trainingId: number) => {
    setSelectedTrainings((prev) =>
      prev.includes(trainingId)
        ? prev.filter((id) => id !== trainingId)
        : [...prev, trainingId]
    );
  };

  const handleTrainingDocumentChange = (
    trainingId: number,
    file: File,
    name: string
  ) => {
    if (!file || !name) return;
    if (file.size > MAX_FILE_SIZE) {
      toast.error(
        `File size must be less than ${MAX_FILE_SIZE / 1024 / 1024}MB`
      );
      return;
    }
    setTrainingDocuments((prev) => {
      const docs = prev[trainingId] || [];
      return {
        ...prev,
        [trainingId]: [...docs, { file, name, isPublic: true }],
      };
    });
  };

  const handleRemoveTrainingDocument = (trainingId: number, idx: number) => {
    setTrainingDocuments((prev) => {
      const docs = prev[trainingId] || [];
      return {
        ...prev,
        [trainingId]: docs.filter((_, i) => i !== idx),
      };
    });
  };

  const handleGeneralDocumentChange = (file: File, name: string) => {
    if (!file || !name) return;
    if (file.size > MAX_FILE_SIZE) {
      toast.error(
        `File size must be less than ${MAX_FILE_SIZE / 1024 / 1024}MB`
      );
      return;
    }
    setGeneralDocuments((prev) => [...prev, { file, name, isPublic: true }]);
  };

  const handleRemoveGeneralDocument = (idx: number) => {
    setGeneralDocuments((prev) => prev.filter((_, i) => i !== idx));
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-4xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Create New Worker</h2>
        {onCancel && (
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        )}
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Photo Upload Section */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-medium mb-4">Worker Photo</h3>
          <div className="flex items-center space-x-4">
            {photoPreview ? (
              <img
                src={photoPreview}
                alt="Worker preview"
                className="w-24 h-24 rounded-full object-cover border-2 border-gray-300"
              />
            ) : (
              <div className="w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center">
                <User className="h-12 w-12 text-gray-400" />
              </div>
            )}

            <div className="flex flex-col space-y-2">
              <label className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <Upload className="h-4 w-4 mr-2" />
                Upload Photo
                <input
                  type="file"
                  accept="image/*"
                  onChange={handlePhotoSelect}
                  className="sr-only"
                />
              </label>

              <button
                type="button"
                onClick={handleTakePhoto}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <Camera className="h-4 w-4 mr-2" />
                Take Photo
              </button>
            </div>
          </div>
          <p className="text-sm text-gray-500 mt-2">
            Photo upload and camera functionality are independent of form
            validation
          </p>
        </div>

        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <User className="h-4 w-4 inline mr-1" />
              Full Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.name ? "border-red-500" : "border-gray-300"
                }`}
              placeholder="Enter full name"
            />
            {errors.name && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.name}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Building className="h-4 w-4 inline mr-1" />
              Company *
            </label>
            <input
              type="text"
              value={formData.company}
              onChange={(e) => handleInputChange("company", e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.company ? "border-red-500" : "border-gray-300"
                }`}
              placeholder="Enter company name"
            />
            {errors.company && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.company}
              </p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <IdCard className="h-4 w-4 inline mr-1" />
              National ID *
            </label>
            <input
              type="text"
              value={formData.nationalId}
              onChange={(e) => handleInputChange("nationalId", e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.nationalId ? "border-red-500" : "border-gray-300"
                }`}
              placeholder="Enter national ID"
            />
            {errors.nationalId && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.nationalId}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Gender *
            </label>
            <select
              value={formData.gender}
              onChange={(e) => handleInputChange("gender", e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.gender ? "border-red-500" : "border-gray-300"
                }`}
            >
              {/* <option value="">Select gender</option> */}
              <option value="MALE">Male</option>
              <option value="FEMALE">Female</option>
              {/* <option value="Other">Other</option> */}
            </select>
            {errors.gender && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.gender}
              </p>
            )}
          </div>
        </div>

        {/* Contact Information */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Phone className="h-4 w-4 inline mr-1" />
              Phone Number *
            </label>
            <input
              type="tel"
              value={formData.phoneNumber}
              onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.phoneNumber ? "border-red-500" : "border-gray-300"
                }`}
              placeholder="+254 712 345 678"
            />
            {errors.phoneNumber && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.phoneNumber}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Mail className="h-4 w-4 inline mr-1" />
              Email Address
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.email ? "border-red-500" : "border-gray-300"
                }`}
              placeholder="<EMAIL>"
            />
            {errors.email && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.email}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Phone className="h-4 w-4 inline mr-1" />
              M-Pesa Number
            </label>
            <input
              type="tel"
              value={formData.mpesaNumber}
              onChange={(e) => handleInputChange("mpesaNumber", e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.mpesaNumber ? "border-red-500" : "border-gray-300"
                }`}
              placeholder="+254 712 345 678"
            />
            {errors.mpesaNumber && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.mpesaNumber}
              </p>
            )}
          </div>
        </div>

        {/* Date Information */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Calendar className="h-4 w-4 inline mr-1" />
              Date of Birth
            </label>
            <input
              type="date"
              value={formData.dateOfBirth}
              onChange={(e) => handleInputChange("dateOfBirth", e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.dateOfBirth ? "border-red-500" : "border-gray-300"
                }`}
            />
            {errors.dateOfBirth && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.dateOfBirth}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Induction Date
            </label>
            <input
              type="date"
              value={formData.inductionDate}
              onChange={(e) =>
                handleInputChange("inductionDate", e.target.value)
              }
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.inductionDate ? "border-red-500" : "border-gray-300"
                }`}
            />
            {errors.inductionDate && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.inductionDate}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Medical Check Date
            </label>
            <input
              type="date"
              value={formData.medicalCheckDate}
              onChange={(e) =>
                handleInputChange("medicalCheckDate", e.target.value)
              }
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.medicalCheckDate ? "border-red-500" : "border-gray-300"
                }`}
            />
            {errors.medicalCheckDate && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.medicalCheckDate}
              </p>
            )}
          </div>
        </div>

        {/* Error message for dropdown loading failures */}
        {hasDropdownErrors && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Failed to load dropdown data
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>
                    Unable to load skills, trades, or trainings. Please refresh
                    the page and try again.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Skills, Training, and Trades Dropdowns */}
        {/* --- TRAINING SECTION (full width) --- */}
        <div className="mt-8">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Training
          </label>
          <div className="border border-gray-300 rounded-md p-3 max-h-60 overflow-y-auto">
            {isLoadingDropdownData ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                <span className="ml-2 text-sm text-gray-500">
                  Loading trainings...
                </span>
              </div>
            ) : (
              trainings.map((training: { id: number; name: string }) => (
                <div key={training.id} className="mb-3">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={selectedTrainings.includes(training.id)}
                      onChange={() => handleTrainingCheckbox(training.id)}
                      className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                    />
                    <span className="text-sm">{training.name}</span>
                  </label>
                  {selectedTrainings.includes(training.id) && (
                    <div className="ml-6 mt-2 space-y-2">
                      <p className="text-sm text-gray-700">
                        Upload documents for {training.name}, if any. E.g certificates
                      </p>
                      {/* List uploaded docs for this training */}
                      {(trainingDocuments[training.id] || []).map(
                        (doc, idx) => (
                          <div
                            key={idx}
                            className="flex items-center space-x-2 text-md border border-gray-300 rounded-md p-2 w-fit"
                          >
                            <span className="text-xs text-gray-700 m-2 flex items-center">
                              <FileIcon className="h-4 w-4 mr-1" />
                              {doc.file.name}
                            </span>
                            <span className="text-xs text-gray-700 mr-2">
                              {doc.name}
                            </span>
                            <button
                              type="button"
                              onClick={() =>
                                handleRemoveTrainingDocument(training.id, idx)
                              }
                              className="text-xs text-red-500"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </div>
                        )
                      )}
                      {/* Upload new doc for this training */}
                      <TrainingDocUploader
                        trainingId={training.id}
                        onAdd={handleTrainingDocumentChange}
                      />
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </div>

        {/* Skills and Trades side by side */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Skills
            </label>
            <div className="border border-gray-300 rounded-md p-3 max-h-40 overflow-y-auto">
              {isLoadingDropdownData ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                  <span className="ml-2 text-sm text-gray-500">
                    Loading skills...
                  </span>
                </div>
              ) : skillsError ? (
                <div className="text-red-500 text-sm">
                  Failed to load skills
                </div>
              ) : skills.length === 0 ? (
                <div className="text-gray-500 text-sm">No skills available</div>
              ) : (
                skills.map((skill: { id: number; name: string }) => (
                  <label
                    key={skill.id}
                    className="flex items-center space-x-2 mb-2"
                  >
                    <input
                      type="checkbox"
                      checked={formData.skillIds.includes(skill.id)}
                      onChange={() =>
                        handleMultiSelectChange("skillIds", skill.id)
                      }
                      className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                    />
                    <span className="text-sm">{skill.name}</span>
                  </label>
                ))
              )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Trades
            </label>
            <div className="border border-gray-300 rounded-md p-3 max-h-40 overflow-y-auto">
              {isLoadingDropdownData ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                  <span className="ml-2 text-sm text-gray-500">
                    Loading trades...
                  </span>
                </div>
              ) : (
                trades.map((trade: { id: number; name: string }) => (
                  <label
                    key={trade.id}
                    className="flex items-center space-x-2 mb-2"
                  >
                    <input
                      type="checkbox"
                      checked={formData.tradeIds.includes(trade.id)}
                      onChange={() =>
                        handleMultiSelectChange("tradeIds", trade.id)
                      }
                      className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                    />
                    <span className="text-sm">{trade.name}</span>
                  </label>
                ))
              )}
            </div>
          </div>
        </div>

        {/* --- GENERAL DOCUMENTS SECTION --- */}
        <div className="mt-8">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Documents
          </label>
          <div className="border border-gray-300 rounded-md p-3 max-h-60 overflow-y-auto">
            {/* List uploaded docs */}
            {generalDocuments.map((doc, idx) => (
              <div key={idx} className="flex items-center space-x-2 mb-2 text-md border border-gray-300 rounded-md p-2 w-fit">
                <span className="text-xs text-gray-700 m-2 flex items-center">
                  <FileIcon className="h-4 w-4 mr-1" />
                  {doc.file.name}
                </span>
                <span className="text-xs text-gray-700">{doc.name}</span>
                <button
                  type="button"
                  onClick={() => handleRemoveGeneralDocument(idx)}
                  className="text-xs text-red-500"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
            {/* Upload new doc */}
            <GeneralDocUploader onAdd={handleGeneralDocumentChange} />
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Create Worker
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreateWorkerForm;

// --- COMPONENTS for uploading docs ---
export const TrainingDocUploader: React.FC<{
  trainingId: number;
  onAdd: (trainingId: number, file: File, name: string) => void;
}> = ({ trainingId, onAdd }) => {
  const [file, setFile] = useState<File | null>(null);
  const [name, setName] = useState("");
  const [formError, setFormError] = useState<FormErrors>({});
  const fileInput = useRef<HTMLInputElement>(null);
  return (
    <div className="flex items-center space-x-2">
      <div className="flex flex-col space-y-1">
        <input
          type="file"
          ref={fileInput}
          onChange={(e) => setFile(e.target.files?.[0] || null)}
          className="text-xs"
        />
        {formError.file && (
          <p className="text-xs text-red-500">{formError.file}</p>
        )}
      </div>
      <div className="flex flex-col space-y-1">
        <input
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Document name"
          className="text-xs border rounded px-1 py-0.5"
        />
        {formError.name && (
          <p className="text-xs text-red-500">{formError.name}</p>
        )}
      </div>
      <button
        type="submit"
        className="text-xs text-green-600 flex items-center border border-green-600 rounded px-2 py-1"
        onClick={(e) => {
          e.preventDefault();
          if (!file) {
            setFormError({ file: "Please select a file" });
            return;
          }
          if (!name) {
            setFormError({ name: "Please enter a name for the document" });
            return;
          }
          if (file && name) {
            onAdd(trainingId, file, name);
            setFile(null);
            setName("");
            setFormError({});
            fileInput.current!.value = "";
          }
        }}
      >
        <Plus className="h-4 w-4 mr-1" />
        Add
      </button>
    </div>
  );
};

export const GeneralDocUploader: React.FC<{
  onAdd: (file: File, name: string) => void;
}> = ({ onAdd }) => {
  const [file, setFile] = useState<File | null>(null);
  const [name, setName] = useState("");
  const fileInput = useRef<HTMLInputElement>(null);
  const [formError, setFormError] = useState<FormErrors>({});
  return (
    <div className="flex items-center space-x-2">
      <div className="flex flex-col space-y-1">
        <input
          type="file"
          ref={fileInput}
          onChange={(e) => setFile(e.target.files?.[0] || null)}
          className="text-xs"
        />
        {formError.file && (
          <p className="text-xs text-red-500">{formError.file}</p>
        )}
      </div>
      <div className="flex flex-col space-y-1">
        <input
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Document name"
          className="text-xs border rounded px-1 py-0.5"
        />
        {formError.name && (
          <p className="text-xs text-red-500">{formError.name}</p>
        )}
      </div>
      <button
        type="submit"
        className="text-xs text-green-600 flex items-center border border-green-600 rounded px-2 py-1"
        onClick={(e) => {
          e.preventDefault();
          if (!file) {
            setFormError({ file: "Please select a file" });
            return;
          }
          if (!name) {
            setFormError({ name: "Please enter a name for the document" });
            return;
          }
          if (file && name) {
            onAdd(file, name);
            setFile(null);
            setName("");
            setFormError({});
            fileInput.current!.value = "";
          }
        }}
      >
        <Plus className="h-4 w-4 mr-1" />
        Add
      </button>
    </div>
  );
};
